const mysql = require('mysql2/promise');
require('dotenv').config();

// Create connection pool for better performance and reliability
const pool = mysql.createPool({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT || 3306,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    connectTimeout: 60000,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    ssl: false
});

// Test the connection
const testConnection = async () => {
    console.log('🔍 Testing database connection...');
    console.log(`📍 Connecting to: ${process.env.DB_HOST}:${process.env.DB_PORT || 3306}`);
    console.log(`👤 User: ${process.env.DB_USER}`);
    console.log(`🗄️  Database: ${process.env.DB_NAME}`);

    try {
        const connection = await pool.getConnection();
        console.log('✅ Connected to MySQL Database with connection pool');

        // Test a simple query
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log('✅ Database query test successful');

        connection.release();
    } catch (err) {
        console.error('❌ DB connection failed:', err.message);
        console.error('🔧 Error code:', err.code);
        console.error('🔧 Error errno:', err.errno);

        // Provide specific troubleshooting tips
        if (err.code === 'ETIMEDOUT') {
            console.log('💡 Troubleshooting tips:');
            console.log('   - Check if the database server is running');
            console.log('   - Verify the host IP address and port');
            console.log('   - Check firewall settings');
            console.log('   - Ensure network connectivity');
        } else if (err.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('💡 Troubleshooting tips:');
            console.log('   - Check username and password');
            console.log('   - Verify user has access to the database');
        } else if (err.code === 'ER_BAD_DB_ERROR') {
            console.log('💡 Troubleshooting tips:');
            console.log('   - Check if the database exists');
            console.log('   - Verify database name spelling');
        }

        console.log('⚠️  Server will continue running, but database operations will fail');
    }
};

testConnection();

module.exports = pool;