const db = require('../config/db');

const insertInquiry = async (req, res) => {
    try {
        const { name, mail, phone, message } = req.body;
        const input = [name, mail, phone, message];
        const sql = "CALL insert_inquiry(?,?,?,?)";

        const [results] = await db.execute(sql, input);

        return res.status(200).json({
            message: 'Data inserted successfully',
            success: true
        });
    } catch (err) {
        console.error('Error inserting data:', err);
        return res.status(500).json({
            error: 'Database error',
            success: false,
            message: err.message
        });
    }
};

module.exports = { insertInquiry };