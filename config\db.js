const mysql = require('mysql2/promise');
require('dotenv').config();

// Create connection pool for better performance and reliability
const pool = mysql.createPool({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// Test the connection
const testConnection = async () => {
    try {
        const connection = await pool.getConnection();
        console.log('Connected to MySQL Database with connection pool');
        connection.release();
    } catch (err) {
        console.error('DB connection failed:', err.message);
        console.log('Server will continue running, but database operations will fail');
    }
};

testConnection();

module.exports = pool;