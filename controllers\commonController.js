const db=require('../config/db')


export const insertInquiry= async(req,res)=>{
    const { name, mail, phone, message } = req.body;
    const input=[name,mail,phone,message]
    const sql= "CALL insert_inquiry(?,?,?,?)";
    db.execute(sql,input,(err,results,fields)=>{
        if (err) {
            console.error('Error inserting data:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        return res.status(200).json({ message: 'Data inserted successfully' });
    });
    
}