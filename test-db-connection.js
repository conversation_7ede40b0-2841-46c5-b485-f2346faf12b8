// Database connection test script
// Run with: node test-db-connection.js

require('dotenv').config();
const mysql = require('mysql2/promise');

async function testDatabaseConnection() {
    console.log('🔍 Testing Database Connection...\n');
    
    // Display connection parameters
    console.log('📋 Connection Parameters:');
    console.log(`   Host: ${process.env.DB_HOST}`);
    console.log(`   Port: ${process.env.DB_PORT || 3306}`);
    console.log(`   User: ${process.env.DB_USER}`);
    console.log(`   Database: ${process.env.DB_NAME}`);
    console.log(`   Password: ${'*'.repeat(process.env.DB_PASSWORD?.length || 0)}\n`);

    try {
        // Test 1: Basic connection
        console.log('🔗 Test 1: Basic Connection...');
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
            port: process.env.DB_PORT || 3306,
            connectTimeout: 10000
        });
        console.log('✅ Basic connection successful');

        // Test 2: Simple query
        console.log('🔍 Test 2: Simple Query...');
        const [rows] = await connection.execute('SELECT 1 as test, NOW() as current_time');
        console.log('✅ Query successful:', rows[0]);

        // Test 3: Check if database exists
        console.log('🔍 Test 3: Database Existence...');
        const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [process.env.DB_NAME]);
        if (databases.length > 0) {
            console.log('✅ Database exists');
        } else {
            console.log('❌ Database does not exist');
        }

        // Test 4: Check if inquiry table exists
        console.log('🔍 Test 4: Table Existence...');
        try {
            const [tables] = await connection.execute('SHOW TABLES LIKE ?', ['inquiry']);
            if (tables.length > 0) {
                console.log('✅ inquiry table exists');
                
                // Test 5: Check table structure
                console.log('🔍 Test 5: Table Structure...');
                const [columns] = await connection.execute('DESCRIBE inquiry');
                console.log('✅ Table structure:');
                columns.forEach(col => {
                    console.log(`   - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
                });
            } else {
                console.log('❌ inquiry table does not exist');
                console.log('💡 Run the database setup script: mysql -u username -p lumora < database/setup.sql');
            }
        } catch (tableError) {
            console.log('❌ Error checking table:', tableError.message);
        }

        // Test 6: Check if stored procedure exists
        console.log('🔍 Test 6: Stored Procedure Existence...');
        try {
            const [procedures] = await connection.execute(
                'SELECT ROUTINE_NAME FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = ?',
                [process.env.DB_NAME, 'insert_inquiry']
            );
            if (procedures.length > 0) {
                console.log('✅ insert_inquiry stored procedure exists');
            } else {
                console.log('❌ insert_inquiry stored procedure does not exist');
                console.log('💡 Run the database setup script to create the stored procedure');
            }
        } catch (procError) {
            console.log('❌ Error checking stored procedure:', procError.message);
        }

        await connection.end();
        console.log('\n🎉 Database connection test completed successfully!');

    } catch (error) {
        console.error('\n❌ Database connection failed:');
        console.error('   Error:', error.message);
        console.error('   Code:', error.code);
        console.error('   Errno:', error.errno);

        console.log('\n🔧 Troubleshooting Steps:');
        
        if (error.code === 'ETIMEDOUT') {
            console.log('   1. Check if MySQL server is running');
            console.log('   2. Verify the host IP address and port');
            console.log('   3. Check firewall settings');
            console.log('   4. Test network connectivity: ping ' + process.env.DB_HOST);
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('   1. Verify username and password');
            console.log('   2. Check user permissions');
            console.log('   3. Ensure user can connect from your IP');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.log('   1. Check if database "' + process.env.DB_NAME + '" exists');
            console.log('   2. Create database: CREATE DATABASE ' + process.env.DB_NAME + ';');
        } else if (error.code === 'ENOTFOUND') {
            console.log('   1. Check if the hostname is correct');
            console.log('   2. Verify DNS resolution');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('   1. Check if MySQL is running on the specified port');
            console.log('   2. Verify the port number (default: 3306)');
        }
        
        console.log('\n📝 Current .env file should look like:');
        console.log('   DB_HOST=your_host_ip');
        console.log('   DB_USER=your_username');
        console.log('   DB_PASSWORD=your_password');
        console.log('   DB_NAME=lumora');
        console.log('   DB_PORT=3306');
    }
}

// Run the test
testDatabaseConnection();
